// 请求头管理模块
// 处理请求头的设置和清理，使用Chrome declarativeNetRequest API

import type { Response } from "../../types/network"

export class HeaderManager {
  // 存储请求头规则的映射 (requestId -> ruleId)
  private requestHeaderRules = new Map<string, number>()

  // 检测是否为HLS流
  private isHLSStream(url: string, type?: string): boolean {
    return url.includes('.m3u8') ||
      type === 'application/vnd.apple.mpegurl' ||
      type === 'application/x-mpegurl'
  }

  // 处理设置请求头请求
  async handleSetRequestHeaders(
    payload: { url: string, requestHeaders?: chrome.webRequest.HttpHeader[], pageUrl?: string, tabId: number, pageTaskId?: string },
    sendResponse: (response: Response) => void
  ) {
    try {
      const { url, requestHeaders, pageUrl, tabId, pageTaskId } = payload

      if (!url) {
        sendResponse({
          success: false,
          error: "缺少URL参数"
        })
        return
      }

      // 如果没有tabId，使用一个固定的规则ID用于全局规则
      const ruleId = tabId || 999999

      // 准备请求头列表
      let headersToSet: chrome.webRequest.HttpHeader[] = []

      // 检测是否为 M3U8 流
      const isM3U8 = this.isHLSStream(url)

      // 如果有原始请求头，先过滤掉浏览器禁止修改的请求头
      if (requestHeaders && requestHeaders.length > 0) {
        const allowedHeaders = requestHeaders.filter((header: chrome.webRequest.HttpHeader) => {
          const name = header.name.toLowerCase()
          // 排除浏览器禁止修改的请求头
          const forbiddenHeaders = [
            'host', 'content-length', 'connection', 'upgrade',
            'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
          ]
          // 排除请求大小相关的请求头（避免部分下载，确保下载完整文件）
          const sizeRelatedHeaders = [
            'range', 'if-range', 'content-range', 'accept-ranges',
            'content-length', 'transfer-encoding', 'if-modified-since',
            'if-none-match', 'if-unmodified-since', 'if-match'
          ]
          return !forbiddenHeaders.includes(name) && !sizeRelatedHeaders.includes(name)
        })
        headersToSet = [...allowedHeaders]

        console.log(`过滤后的请求头数量: ${headersToSet.length}，原始数量: ${requestHeaders.length}`)
      }

      // 确保添加 Referer 请求头
      if (pageUrl) {
        const hasReferer = headersToSet.some(header =>
          header.name.toLowerCase() === 'referer'
        )

        if (!hasReferer) {
          headersToSet.push({
            name: 'Referer',
            value: pageUrl
          })
          console.log(`为标签页 ${tabId} 添加 Referer: ${pageUrl}`)
        }
      }

      // 为 M3U8 添加特殊的请求头
      if (isM3U8) {
        console.log(`检测到 M3U8 流，添加特殊请求头`)

        // 确保有 User-Agent
        const hasUserAgent = headersToSet.some(header =>
          header.name.toLowerCase() === 'user-agent'
        )

        if (!hasUserAgent) {
          headersToSet.push({
            name: 'User-Agent',
            value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          })
        }

        // 添加 Accept 头用于 HLS 流
        const hasAccept = headersToSet.some(header =>
          header.name.toLowerCase() === 'accept'
        )

        if (!hasAccept) {
          headersToSet.push({
            name: 'Accept',
            value: 'application/vnd.apple.mpegurl, application/x-mpegurl, application/mpegurl, video/mp2t, */*'
          })
        }

        // 确保有 Origin（如果有页面URL）
        if (pageUrl) {
          const hasOrigin = headersToSet.some(header =>
            header.name.toLowerCase() === 'origin'
          )

          if (!hasOrigin) {
            try {
              const pageUrlObj = new URL(pageUrl)
              headersToSet.push({
                name: 'Origin',
                value: `${pageUrlObj.protocol}//${pageUrlObj.host}`
              })
            } catch (e) {
              console.warn('无法解析页面 URL 生成 Origin:', e)
            }
          }
        }
      }

      if (headersToSet.length === 0) {
        console.log('没有可设置的请求头')
        sendResponse({ success: true })
        return
      }

      // 构建资源类型列表
      const resourceTypes: chrome.declarativeNetRequest.ResourceType[] = [
        chrome.declarativeNetRequest.ResourceType.MEDIA,
        chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
        chrome.declarativeNetRequest.ResourceType.OTHER
      ]

      // 构建更精确的URL匹配规则
      const escapedUrl = url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

      // 构建规则条件
      const condition: chrome.declarativeNetRequest.RuleCondition = {
        regexFilter: `^${escapedUrl}(\\?.*)?$`,
        resourceTypes
      }

      // 如果有tabId，添加到条件中（类似cat-catch的做法）
      if (tabId) {
        condition.tabIds = [tabId]
      }

      const rule: chrome.declarativeNetRequest.Rule = {
        id: ruleId,
        priority: isM3U8 ? 2 : 1,
        action: {
          type: chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,
          requestHeaders: headersToSet.map(header => ({
            header: header.name,
            operation: chrome.declarativeNetRequest.HeaderOperation.SET,
            value: header.value || ""
          }))
        },
        condition
      }

      // 先移除可能存在的旧规则，再添加新规则（使用SessionRules如cat-catch）
      await chrome.declarativeNetRequest.updateSessionRules({
        removeRuleIds: [ruleId],
        addRules: [rule]
      })

      // 存储规则ID映射
      this.requestHeaderRules.set(tabId.toString(), ruleId)

      console.log(`已设置${isM3U8 ? 'M3U8' : ''}请求头规则:`)
      console.log(`  标签页ID: ${tabId || '无限制(全局规则)'}`)
      console.log(`  规则ID: ${ruleId}`)
      console.log(`  目标URL: ${url}`)
      console.log(`  请求头数量: ${headersToSet.length}`)
      console.log(`  规则范围: ${tabId ? '限制到特定标签页' : '全局规则'}`)

      // 先回复给调用者
      sendResponse({
        success: true,
        message: "请求头规则已设置"
      })

      // 然后向特定标签页发送通知消息
      if (pageTaskId) {
        try {
          await chrome.tabs.sendMessage(tabId, {
            type: 'HEADERS_SET_COMPLETED',
            data: {
              success: true,
              message: "请求头规则已设置",
              pageTaskId: pageTaskId,
              url: url
            }
          })
          console.log(`已向标签页 ${tabId} 发送请求头设置完成通知`)
        } catch (error) {
          console.error(`向标签页 ${tabId} 发送通知失败:`, error)
        }
      }
    } catch (error) {
      console.error("设置请求头失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "设置请求头失败"
      })
    }
  }
}
